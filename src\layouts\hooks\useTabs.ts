import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import type { TabItem } from '../types';
import { ROUTE_TAB_MAPPING, TAB_LABELS } from '../constants.ts';

/**
 * 标签页管理 Hook
 */
export const useTabs = () => {
  const [activeTabKey, setActiveTabKey] = useState('dashboard');
  const [tabs, setTabs] = useState<TabItem[]>([
    {
      key: 'dashboard',
      label: '仪表板',
      closable: false,
    },
  ]);

  const navigate = useNavigate();
  const location = useLocation();

  // 初始化标签页状态
  useEffect(() => {
    const path = location.pathname;
    const routeInfo = ROUTE_TAB_MAPPING[path];

    if (!routeInfo) return;

    const { key, label } = routeInfo;
    setActiveTabKey(key);

    // 确保当前页面的标签页存在
    setTabs(prev => {
      const existingTab = prev.find(tab => tab.key === key);
      if (!existingTab && key !== 'dashboard') {
        return [
          ...prev,
          {
            key,
            label,
            closable: true,
          },
        ];
      }
      return prev;
    });
  }, [location.pathname]);

  // 添加标签页
  const addTab = (key: string, label: string) => {
    const existingTab = tabs.find(tab => tab.key === key);
    if (!existingTab) {
      setTabs(prev => [
        ...prev,
        {
          key,
          label,
          closable: key !== 'dashboard',
        },
      ]);
    }
    setActiveTabKey(key);
  };

  // 移除标签页
  const removeTab = (targetKey: string) => {
    const newTabs = tabs.filter(tab => tab.key !== targetKey);
    setTabs(newTabs);

    if (activeTabKey === targetKey) {
      const lastTab = newTabs[newTabs.length - 1];
      if (lastTab) {
        setActiveTabKey(lastTab.key);
        navigate(`/${lastTab.key === 'dashboard' ? '' : lastTab.key}`);
      }
    }
  };

  // 标签页切换
  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    navigate(`/${key === 'dashboard' ? '' : key}`);
  };

  // 处理菜单点击，添加对应标签页
  const handleMenuClick = ({ key }: { key: string }) => {
    const label = TAB_LABELS[key];
    if (label) {
      addTab(key, label);
    }

    switch (key) {
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'users':
        navigate('/users');
        break;
      case 'tasks':
        navigate('/tasks');
        break;
      case 'alerts':
        navigate('/alerts');
        break;
      case 'teams':
        navigate('/teams');
        break;
      default:
        break;
    }
  };

  return {
    activeTabKey,
    tabs,
    addTab,
    removeTab,
    handleTabChange,
    handleMenuClick,
  };
};
